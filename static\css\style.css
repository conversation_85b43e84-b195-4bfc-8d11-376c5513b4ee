/* 日次部門別レポート画面スタイル */

/* 基本設定 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 95vw;
    margin: 0 auto;
    padding: 20px;
    min-width: 1200px;
}

/* ヘッダー */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    text-align: center;
    font-size: 2.2rem;
    font-weight: 300;
}

/* フィルター部分 */
.filter-section {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background-color: white;
    min-width: 150px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #667eea;
}

.filter-group select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* ローディング */
.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.1rem;
    color: #666;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* テーブル */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.table-header h2 {
    color: #495057;
    font-size: 1.3rem;
    margin-bottom: 5px;
}

.table-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.table-wrapper {
    overflow-x: auto;
    max-height: calc(100vh - 400px);
    min-height: 400px;
    overflow-y: auto;
    position: relative;
    border: 1px solid #dee2e6;
    width: 100%;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    min-width: 1400px;
}

th {
    background: #495057;
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 100;
    white-space: nowrap;
    border-bottom: 2px solid #343a40;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ソート可能なヘッダー */
th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

th.sortable:hover {
    background: #5a6268;
}

/* ソートアイコン */
.sort-icon {
    font-size: 0.8rem;
    margin-left: 5px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

th.sortable:hover .sort-icon {
    opacity: 1;
}

/* ソート状態のスタイル */
th.sort-asc .sort-icon::before {
    content: '▲';
    color: #28a745;
    font-weight: bold;
}

th.sort-desc .sort-icon::before {
    content: '▼';
    color: #dc3545;
    font-weight: bold;
}

th.sort-asc .sort-icon,
th.sort-desc .sort-icon {
    opacity: 1;
}

/* デフォルトのソートアイコンを隠す */
th.sort-asc .sort-icon,
th.sort-desc .sort-icon {
    font-size: 0;
}

th.sort-asc .sort-icon::before,
th.sort-desc .sort-icon::before {
    font-size: 0.8rem;
}

td {
    padding: 10px 8px;
    border-bottom: 1px solid #dee2e6;
    text-align: center;
    white-space: nowrap;
    color: #000;
    font-weight: 500;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

/* 数値列のスタイル */
.number {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

/* 棚卸関連列のスタイル */
th[data-column="棚卸日"],
th[data-column="単品棚卸売価"],
th[data-column="部門棚卸売価"] {
    background: #6c757d;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 2px solid #5a6268;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

th[data-column="棚卸日"]:hover,
th[data-column="単品棚卸売価"]:hover,
th[data-column="部門棚卸売価"]:hover {
    background: #5a6268;
}

/* エラーメッセージ */
.error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
    margin: 20px 0;
}

/* 大画面対応 */
@media (min-width: 1600px) {
    .container {
        max-width: 96vw;
        padding: 25px;
    }

    table {
        font-size: 1rem;
        min-width: 1600px;
    }

    th, td {
        padding: 14px 10px;
    }

    .table-wrapper {
        max-height: calc(100vh - 350px);
    }
}

@media (min-width: 1920px) {
    .container {
        max-width: 97vw;
        padding: 30px;
    }

    table {
        min-width: 1800px;
    }
}

/* レスポンシブ対応 */
@media (max-width: 1200px) {
    .container {
        max-width: 98vw;
        min-width: auto;
        padding: 15px;
    }

    table {
        min-width: 1200px;
        font-size: 0.85rem;
    }

    th, td {
        padding: 10px 6px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
        max-width: 100vw;
        min-width: auto;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .filter-group input,
    .filter-group select {
        min-width: 100%;
    }

    .btn {
        width: 100%;
        margin-top: 10px;
    }

    .table-wrapper {
        max-height: calc(100vh - 300px);
    }

    table {
        font-size: 0.8rem;
        min-width: 1000px;
    }

    th, td {
        padding: 8px 4px;
    }
}

/* 印刷用スタイル */
@media print {
    body {
        background: white;
        color: #000;
    }

    .filter-section {
        display: none;
    }

    .table-container {
        box-shadow: none;
    }

    .table-wrapper {
        max-height: none;
        overflow: visible;
        border: none;
    }

    th {
        background: #333 !important;
        color: white !important;
        position: static !important;
        box-shadow: none !important;
    }

    td {
        color: #000 !important;
    }
}
